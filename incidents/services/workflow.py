from api.views.auth.permissions_list import is_admin_user, is_super_user
from base.constants import ReviewStatus
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse, RepositoryResponse
from typing import Type, List
from django.core.exceptions import ValidationError
from django.db.models import Model
from django.contrib.auth.models import User
from rest_framework import status

from incidents.services.workflow_utils import WorkFlowUtils

logging_service = LoggingService()


class IncidentWorkflow:
    def __init__(self, user, model: Model):
        self.model = model
        self.user = user
        self.workflow_utils = WorkFlowUtils(user=user, model=model)

    def send_for_a_review(self, incident_id, data) -> APIResponse:
        """Handle sending incident for review"""
        try:

            # check permissions
            if "review_template" in data and "assignees" in data:
                return APIResponse(
                    success=False,
                    message="Incident can not be reviewed by individuals and group at the same time",
                    data=None,
                    code=400,
                )
            if not "review_template" in data and not "assignees" in data:
                return APIResponse(
                    success=False,
                    message="Assignees or review template is required",
                    data=None,
                    code=400,
                )
            if "review_template" in data:
                """handle review template"""
                response = self.workflow_utils.handle_review_template(
                    review_template_id=data["review_template"],
                    incident_id=incident_id,
                )

                if not response.success:
                    return APIResponse(
                        success=False,
                        message=response.message,
                        data=None,
                        code=400,
                    )

                return APIResponse(
                    success=True,
                    message="Incident sent for review successfully",
                    data=response.data,
                    code=200,
                )

            elif "assignees" in data:
                """handle assignees"""
                response = self.workflow_utils.handle_assigned_members(
                    data=data,
                    incident_id=incident_id,
                )

                if not response.success:
                    return APIResponse(
                        success=False,
                        message=response.message,
                        data=None,
                        code=400,
                    )

                return APIResponse(
                    success=True,
                    message="Incident sent for review successfully",
                    data=response.data,
                    code=200,
                )
            else:
                return APIResponse(
                    success=False,
                    message="Assignees or review template is required",
                    data=None,
                    code=400,
                )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error sending incident for review",
                data=None,
                code=500,
            )

    def mark_as_resolved(self, incident, user, is_dynamic=False) -> APIResponse:
        try:
            if not is_super_user(user) and not is_admin_user(
                user, incident.report_facility
            ):
                return APIResponse(
                    success=False,
                    message="You do not have permission to mark incidents as resolved",
                    data=None,
                    code=403,
                )
            incident.updated_by = user
            incident.is_resolved = True
            incident.status = ReviewStatus.CLOSED
            incident.save()

            # get all incident versions and mark them as resolved
            versions = (
                incident.versions.all()
                if not is_dynamic
                else self.get_versions(incident)
            )
            for version in versions:
                version.is_resolved = True
                version.status = ReviewStatus.CLOSED
                version.updated_by = user
                version.save()

            return APIResponse(
                success=True,
                message="Incident marked as resolved",
                data=None,
                code=200,
            )
        except Exception as e:
            LoggingService().log_error(e)

            return APIResponse(
                success=False,
                message="Failed to mark incident as resolved",
                data=None,
                code=500,
            )

    # used to delete other user's drafts
    def delete_drafts(
        self,
        user: User,
        model: Type[Model],
        incident_ids: List[int],
    ) -> RepositoryResponse:
        if not isinstance(incident_ids, list) or not incident_ids:
            return RepositoryResponse(
                success=False,
                message="incident_ids must be a non-empty list",
                data=None,
            )
        if not is_admin_user(user) and not is_super_user(user):
            return RepositoryResponse(
                success=False,
                message="You do not have permission to delete drafts",
                data=None,
            )
        incidents = model.objects.all()

        incidents = model.objects.filter(
            created_by=user,
            status=ReviewStatus.DRAFT,
            id__in=incident_ids,
        )
        if not incidents.exists():
            return RepositoryResponse(
                success=False,
                message=f"No drafts found for user {user.id} with IDs: {incident_ids}",
                data=None,
            )
        try:
            deleted_count, _ = incidents.delete()
            logging_service.log_info(
                f"{deleted_count} drafts deleted for user {user.id}"
            )
            logging_service.log_info(
                f"User {user.id} successfully deleted drafts: {incident_ids}"
            )
            return RepositoryResponse(
                success=True,
                message="Drafts deleted successfully",
                data=None,
            )
        except ValidationError as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Validation error occurred while deleting drafts",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="An error occurred while deleting drafts",
                data=None,
            )

    def get_versions(self, instance: Model):
        model_class = instance.__class__
        for related_object in model_class._meta.related_objects:
            if related_object.related_name and related_object.related_name.endswith(
                "_versions"
            ):
                return getattr(instance, related_object.related_name).all()

        return model_class.objects.none()
