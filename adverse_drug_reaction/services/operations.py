from accounts.models import Profile
from accounts.services.user_profile.service import UserProfileService
from adverse_drug_reaction.serializers import (
    AdverseDrugReactionCreateSerializer,
    AdverseDrugReactionUpdateSerializer,
    ListAdverseDrugReactionSerializer,
)
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from api.views.incidents.general_incident.new_incident import (
    check_anonymous,
    get_patient_profile,
)
from base.models import Department
from base.services.forms import check_user_facility
from base.services.incidents.get_incidents import GetIncidentsService
from base.services.logging.logger import LoggingService
from base.services.notifications import save_notification
from base.services.responses import APIResponse
from adverse_drug_reaction.models import AdverseDrugReaction
from documents.models import Document
from incidents.services.query import IncidentQueryService
from incidents.views.send_to_department import send_incident_submission_email
from reviews.models import Review
from tasks.models import ReviewProcessTasks
from activities.services import ActivityService
from activities.models import ActivityType


user_profile_service = UserProfileService()


class ADRService:
    def __init__(self, user):
        self.logging_service = LoggingService()
        self.user = user
        self.query_service = IncidentQueryService(
            user=user,
            model=AdverseDrugReaction,
            serializer=ListAdverseDrugReactionSerializer,
        )

    """A service class for AdversedrugReaction model"""

    def get_incident_by_id(self, incident_id) -> APIResponse:
        try:
            incident = self.query_service.get_incident_by_id(
                incident_id=incident_id,
            )
            if not incident.success:
                return APIResponse(
                    success=False,
                    message=incident.message,
                    data=None,
                    code=400,
                )

            return APIResponse(
                success=True,
                message="Incident retrieved successfully",
                data=incident.data,
                code=200,
            )
        except AdverseDrugReaction.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def get_incidents_list(
        self,
        filters=None,
    ):
        try:
            incidents = self.query_service.get_incidents(
                user=self.user,
                model=AdverseDrugReaction,
                serializer=ListAdverseDrugReactionSerializer,
                prefetch_fields=[
                    "documents",
                    "reviews",
                    "review_tasks",
                ],
                related_fields=[
                    "patient_name",
                    "report_facility",
                    "department",
                    "created_by",
                    "name_of_physician_notified",
                    "name_of_family_notified",
                    "observers_name",
                ],
                filters=filters,
            )
            if not incidents.success:
                return APIResponse(
                    success=False,
                    message=incidents.message,
                    data=None,
                    code=400,
                )

            return APIResponse(
                success=True,
                message="Incidents retrieved successfully",
                data=incidents.data,
                code=200,
            )
        except Exception as e:
            LoggingService().log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def create_incident(self, data) -> APIResponse:
        try:
            facility_response = check_user_facility(data, self.user)
            if not facility_response.success:
                return APIResponse(
                    success=False,
                    message=facility_response.message,
                    data=None,
                    code=400,
                )
            facility = facility_response.data

            request_data = data.copy()
            document_ids = request_data.pop("documents", [])
            review_ids = request_data.pop("reviews", [])
            review_task_ids = request_data.pop("review_tasks", [])

            if "patient_name" in data:
                patient_profile = user_profile_service.get_or_create_profile(
                    data["patient_name"]
                )
                if not patient_profile.success:
                    return APIResponse(
                        success=False,
                        message=patient_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["patient_name"] = patient_profile.data.id

            if "observers_name" in data:
                observers_profile = user_profile_service.get_or_create_profile(
                    data["observers_name"]
                )
                if not observers_profile.success:
                    return APIResponse(
                        success=False,
                        message=observers_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["observers_name"] = observers_profile.data.id

            if "name_of_physician_notified" in data:
                physician_notified_profile = user_profile_service.get_or_create_profile(
                    data["name_of_physician_notified"]
                )
                if not physician_notified_profile.success:
                    return APIResponse(
                        success=False,
                        message=physician_notified_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["name_of_physician_notified"] = (
                    physician_notified_profile.data.id
                )

            if "name_of_family_notified" in data:
                family_notified_profile = user_profile_service.get_or_create_profile(
                    data["name_of_family_notified"]
                )
                if not family_notified_profile.success:
                    return APIResponse(
                        success=False,
                        message=family_notified_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["name_of_family_notified"] = (
                    family_notified_profile.data.id
                )

            if "notified_by" in data:
                notified_by_profile = user_profile_service.get_or_create_profile(
                    data["notified_by"]
                )
                if not notified_by_profile.success:
                    return APIResponse(
                        success=False,
                        message=notified_by_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["notified_by"] = notified_by_profile.data.id

            request_data["created_by"] = self.user.id
            serializer = AdverseDrugReactionCreateSerializer(data=request_data)

            if serializer.is_valid():
                instance = serializer.save()
                if document_ids:
                    instance.documents.set(Document.objects.filter(id__in=document_ids))
                if review_ids:
                    instance.reviews.set(Review.objects.filter(id__in=review_ids))
                if review_task_ids:
                    instance.review_tasks.set(
                        ReviewProcessTasks.objects.filter(id__in=review_task_ids)
                    )

                # save notification and assign it to quality managers
                save_notification(
                    facility=serializer.data["report_facility"],
                    group_name="Admin",
                    notification_type="info",
                    notification_category="incident",
                    message="A new incident is submitted",
                    item_id=serializer.data["id"],
                )

                ActivityService.log_creation(
                    user=self.user,
                    content_object=instance,
                    facility=facility,
                    details={
                        'incident_type': 'Adverse Drug Reaction',
                        'status': instance.status,
                    }
                )

                if document_ids:
                    ActivityService.log_document_activity(
                        user=self.user,
                        content_object=instance,
                        document_count=len(document_ids),
                        is_addition=True,
                        details={'incident_type': 'Adverse Drug Reaction'}
                    )

                return APIResponse(
                    success=True,
                    message="Incident created successfully",
                    data=serializer.data,
                    code=201,
                )
            self.logging_service.log_error(serializer.errors)
            return APIResponse(
                success=False,
                message="Invalid data",
                data=None,
                code=400,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def update_incident(self, id, data) -> APIResponse:

        try:
            request_data = data.copy()
            adverse_drug_reaction = AdverseDrugReaction.objects.get(id=id)
            profile = Profile.objects.get(user=self.user)
            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, profile.facility)
                and not is_manager_user(self.user, adverse_drug_reaction.department)
                and not self.user == adverse_drug_reaction.created_by
            ):
                return APIResponse(
                    success=False,
                    message="You do not have enough rights to update this incident",
                    data=None,
                    code=403,
                )
            document_ids = request_data.pop("documents", None)
            review_ids = request_data.pop("reviews", None)
            review_task_ids = request_data.pop("review_tasks", None)
            facility = adverse_drug_reaction.report_facility
            request_data["report_facility"] = facility

            if "patient_name" in data:
                patient_profile = user_profile_service.get_or_create_profile(
                    data["patient_name"]
                )
                if not patient_profile.success:
                    return APIResponse(
                        success=False,
                        message=patient_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["patient_name"] = patient_profile.data.id

            if "observers_name" in data:
                observers_profile = user_profile_service.get_or_create_profile(
                    data["observers_name"]
                )
                if not observers_profile.success:
                    return APIResponse(
                        success=False,
                        message=observers_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["observers_name"] = observers_profile.data.id

            if "name_of_physician_notified" in data:
                physician_notified_profile = user_profile_service.get_or_create_profile(
                    data["name_of_physician_notified"]
                )
                if not physician_notified_profile.success:
                    return APIResponse(
                        success=False,
                        message=physician_notified_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["name_of_physician_notified"] = (
                    physician_notified_profile.data.id
                )

            if "name_of_family_notified" in data:
                family_notified_profile = user_profile_service.get_or_create_profile(
                    data["name_of_family_notified"]
                )
                if not family_notified_profile.success:
                    return APIResponse(
                        success=False,
                        message=family_notified_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["name_of_family_notified"] = (
                    family_notified_profile.data.id
                )

            if "facility_id" in data:
                facility_response = self._process_facility(data["facility_id"])
                if not facility_response.success:
                    return APIResponse(
                        success=False,
                        message=facility_response.message,
                        code=400,
                    )
                facility = facility_response.data
                request_data["report_facility"] = facility

            if "notified_by" in data:
                notified_by_profile = user_profile_service.get_or_create_profile(
                    data["notified_by"]
                )
                if not notified_by_profile.success:
                    return APIResponse(
                        success=False,
                        message=notified_by_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["notified_by"] = notified_by_profile.data.id

            if "department" in data:
                department_response = Department.objects.get(id=data["department"])
                if not department_response:
                    return APIResponse(
                        success=False,
                        message=department_response,
                        code=400,
                    )
                request_data["department"] = department_response.id

            data = check_anonymous(request_data, self.user)
            old_status = adverse_drug_reaction.status
            serializer = AdverseDrugReactionUpdateSerializer(
                adverse_drug_reaction, data=data, partial=True
            )
            if serializer.is_valid():
                serializer.save()
                if document_ids is not None:
                    adverse_drug_reaction.documents.set(
                        Document.objects.filter(id__in=document_ids)
                    )
                if review_ids is not None:
                    adverse_drug_reaction.reviews.set(
                        Review.objects.filter(id__in=review_ids)
                    )

                if review_task_ids is not None:
                    adverse_drug_reaction.review_tasks.set(
                        ReviewProcessTasks.objects.filter(id__in=review_task_ids)
                    )

                new_status = data.get("status")

                if old_status != new_status:
                    ActivityService.log_status_change(
                        user=self.user,
                        content_object=adverse_drug_reaction,
                        old_status=old_status,
                        new_status=new_status,
                        details={
                            'incident_type': 'Adverse Drug Reaction',
                            'status_change': 'update_status_change'
                        }
                    )

                changed_fields = list(data.keys())
                ActivityService.log_update(
                    user=self.user,
                    content_object=adverse_drug_reaction,
                    changed_fields=changed_fields,
                    details={
                        'incident_type': 'Adverse Drug Reaction',
                        'update_type': 'incident_update'
                    }
                )

                if document_ids is not None:
                    ActivityService.log_document_activity(
                        user=self.user,
                        content_object=adverse_drug_reaction,
                        document_count=len(document_ids),
                        is_addition=True,
                        details={'incident_type': 'Adverse Drug Reaction'}
                    )
                if "status" in data and data.get("status") == "Open":
                    send_incident_submission_email(
                        incident=adverse_drug_reaction,
                        incident_type="Anaphylaxis/Adverse Drug Reaction",
                    )
                serialized_data = ListAdverseDrugReactionSerializer(
                    adverse_drug_reaction
                )
                return APIResponse(
                    success=True,
                    message="Incident updated successfully",
                    data=serialized_data.data,
                    code=200,
                )
            else:
                self.logging_service.log_error(serializer.errors)
                return APIResponse(
                    success=False,
                    message="Invalid data",
                    data=None,
                    code=400,
                )
        except Profile.DoesNotExist:
            return APIResponse(
                success=False,
                message="Admin's profile not found",
                data=None,
                code=404,
            )
        except AdverseDrugReaction.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )
