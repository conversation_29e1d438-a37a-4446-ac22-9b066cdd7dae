# Task Permissions Update - Explanation

## Summary of Changes

I've updated the `tasks/permissions.py` file to fix several issues and align the permission logic with the actual `ReviewProcessTasks` model structure.

## Issues Fixed

### 1. **Incomplete Constructor**

- **Problem**: The `__init__` method was malformed with `self` alone on a line
- **Fix**: <PERSON>perly implemented the constructor to store user and task references

### 2. **Non-existent "Assignee" Concept**

- **Problem**: Original rules referenced an "assignee" field that doesn't exist in the model
- **Fix**: Replaced with the actual concepts of `reviewers` and `review_groups` that exist in the model

### 3. **Incorrect Field References**

- **Problem**: References to undefined fields like "require all approval"
- **Fix**: Used the actual model fields:
     - `require_approval_for_all_groups`
     - `require_all_members_to_complete`
     - `reviewers`
     - `reviewers_completed`
     - `review_groups`
     - `groups_completed`

## Updated Permission Rules

The corrected permission logic now properly handles these scenarios:

### 1. **Group-Based Tasks with All Groups Required**

```python
if task.require_approval_for_all_groups and is_group_member:
```

- User must be a member of a review group assigned to the task
- Their group must not have completed the task yet
- All groups must complete before the task is considered done

### 2. **Individual Reviewer Tasks with All Members Required**

```python
if task.require_all_members_to_complete and is_reviewer:
```

- User must be assigned as a reviewer to the task
- User must not have already completed their review
- All assigned reviewers must complete before the task is done

### 3. **Standard Tasks (No Special Requirements)**

```python
if not task.require_approval_for_all_groups and not task.require_all_members_to_complete:
```

- User must be either a reviewer or group member
- User must not have already completed the task
- Task can be completed by any single submission

### 4. **Quality Risk Manager Override**

```python
if user.groups.filter(name="Quality/Risk Manager").exists():
```

- Quality Risk Managers can always complete tasks regardless of other restrictions

## Additional Methods Added

### `can_mark_task_completed()`

- Determines if a task meets all requirements to be marked as completed
- Checks if all required approvals have been obtained based on task settings

### `get_pending_approvals()`

- Returns detailed information about who still needs to approve/complete a task
- Useful for UI display and progress tracking
- Returns pending groups, pending reviewers, and completion status

## Model Structure Alignment

The updated permissions now correctly work with the actual model relationships:

```python
# ReviewProcessTasks model fields used:
- reviewers (ManyToManyField to Profile)
- reviewers_completed (ManyToManyField to Profile)
- review_groups (ManyToManyField to ReviewGroups)
- groups_completed (ManyToManyField to ReviewGroups)
- require_approval_for_all_groups (BooleanField)
- require_all_members_to_complete (BooleanField)
```

## Usage Example

```python
# Check if a user can complete a specific task
task_permissions = TaskPermissions(user, task)
can_complete = task_permissions.can_complete_task(user, task)

# Check if task is ready to be marked as completed
can_mark_complete = task_permissions.can_mark_task_completed(task)

# Get pending approval details
pending_info = task_permissions.get_pending_approvals(task)
```

This implementation now accurately reflects the actual model structure and provides robust permission checking for the task completion workflow.
