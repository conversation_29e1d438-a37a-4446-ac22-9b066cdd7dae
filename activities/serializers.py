from activities.models import ActivityLog
from rest_framework import serializers
from django.contrib.auth.models import User
from base.serializers import DepartmentSerializer, FacilitySerializer


class UserSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'email', 'first_name', 'last_name', 'full_name']

    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip() or obj.email


class ActivityLogSerializer(serializers.ModelSerializer):
    """Enhanced serializer for the new ActivityLog model"""
    user = UserSerializer(read_only=True)
    target_user = UserSerializer(read_only=True)
    department = DepartmentSerializer(read_only=True)
    facility = FacilitySerializer(read_only=True)

    formatted_description = serializers.SerializerMethodField()
    activity_icon = serializers.SerializerMethodField()
    content_object_type = serializers.SerializerMethodField()
    content_object_id = serializers.SerializerMethodField()

    incident_id = serializers.CharField(read_only=True)
    action = serializers.CharField(read_only=True)

    class Meta:
        model = ActivityLog
        fields = [
            'id',
            'user',
            'target_user',
            'activity_type',
            'action',
            'timestamp',
            'ip_address',
            'user_agent',
            'description',
            'details',
            'department',
            'facility',
            'content_type',
            'object_id',
            'content_object_type',
            'content_object_id',
            'incident_id',
            'formatted_description',
            'activity_icon',
        ]
        read_only_fields = [
            'id',
            'timestamp',
            'content_type',
            'object_id',
            'formatted_description',
            'activity_icon',
            'content_object_type',
            'content_object_id',
        ]

    def get_formatted_description(self, obj):
        """Get the formatted description for display"""
        return obj.get_formatted_description()

    def get_activity_icon(self, obj):
        """Get the icon for the activity type"""
        return obj.get_activity_icon()

    def get_content_object_type(self, obj):
        """Get the human-readable content object type"""
        if obj.content_type:
            return obj.content_type.model
        return None

    def get_content_object_id(self, obj):
        """Get the content object ID"""
        return obj.object_id


class ActivityLogCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating activity logs (backward compatibility)"""

    class Meta:
        model = ActivityLog
        fields = [
            'action',
            'description',
            'ip_address',
            'user_agent',
            'incident_id',
            'details',
        ]

    def create(self, validated_data):
        action = validated_data.get('action', '')

        activity_type_mapping = {
            'created': 'created',
            'updated': 'updated',
            'status_changed': 'status_changed',
            'sent_to_department': 'sent_to_department',
            'document_added': 'document_added',
            'review_added': 'review_added',
            'assigned': 'assigned',
            'resolved': 'resolved',
        }

        activity_type = activity_type_mapping.get(action.lower(), 'updated')
        validated_data['activity_type'] = activity_type

        return super().create(validated_data)
