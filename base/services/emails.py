import requests

from base.services.logging.logger import LoggingService
from core.settings import EMAIL_SERVER_TOKEN, MAIN_EMAIL, SEND_TEST_EMAILS, ENVIRONMENT

logging_service = LoggingService()
url = "https://api.postmarkapp.com/email"
headers = {
    "X-Postmark-Server-Token": EMAIL_SERVER_TOKEN,
    "Content-Type": "application/json",
}


def send_email(recipient_email, data, html_body, subject):

    # check if recipient email does not include "active"
    print(
        f"Environment: {ENVIRONMENT == 'production'}, SEND_TEST_EMAILS: {SEND_TEST_EMAILS}"
    )
    if not ENVIRONMENT == "production" and SEND_TEST_EMAILS:
        recipient_email = "<EMAIL>"
        print(f"Test email sent to: {recipient_email}")

    body = {
        "From": MAIN_EMAIL,
        "To": recipient_email,
        "Subject": subject,
        "TemplateModel": data,
        "HtmlBody": html_body,
    }

    try:
        response = requests.post(url, headers=headers, json=body)
        print(f"Email sent to {recipient_email}: {response.status_code}")
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        error_body = getattr(e.response, "text", "No response body")
        error_status = getattr(e.response, "status_code", "Unknown status code")

        error_message = f"Error sending email: Status code: {error_status}\nResponse body: {error_body}"
        logging_service.log_error(error_message)

        # raise RuntimeError(f"Failed to send email: {"Internal server error"}. {error_message}") from e
        pass
