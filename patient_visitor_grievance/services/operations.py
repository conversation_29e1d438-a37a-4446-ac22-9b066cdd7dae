from accounts.models import Profile
from accounts.services.user_profile.service import UserProfileService
from patient_visitor_grievance.serializers import (
    GrievanceInvestigationSerializer,
    GrievanceInvestigationUpdateSerializer,
    GrievanceSerializer,
    GrievanceUpdateSerializer,
)
from patient_visitor_grievance.new_serializers import (
    GetPatientVisitorGrievanceSerializer,
    GetGrievanceInvestigationSerializer,
)
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from api.views.incidents.general_incident.new_incident import check_anonymous
from base.models import Department
from base.services.forms import check_user_facility
from base.services.logging.logger import LoggingService
from base.services.notifications import save_notification
from base.services.responses import APIResponse
from patient_visitor_grievance.models import (
    Grievance,
    GrievanceInvestigation,
    GrievanceInvestigationInvolvedParty,
)
from documents.models import Document
from incidents.services.query import IncidentQueryService
from incidents.views.send_to_department import send_incident_submission_email
from reviews.models import Review
from tasks.models import ReviewProcessTasks
from activities.services import ActivityService
from activities.models import ActivityType


user_profile_service = UserProfileService()


class GrievanceService:
    def __init__(self, user):
        self.logging_service = LoggingService()
        self.user = user
        self.query_service = IncidentQueryService(
            user=user,
            model=Grievance,
            serializer=GetPatientVisitorGrievanceSerializer,
        )

    """A service class for Grievance model"""

    def get_incident_by_id(self, incident_id) -> APIResponse:
        try:
            incident = self.query_service.get_incident_by_id(
                incident_id=incident_id,
            )
            if not incident.success:
                return APIResponse(
                    success=False,
                    message=incident.message,
                    data=None,
                    code=400,
                )

            return APIResponse(
                success=True,
                message="Grievance retrieved successfully",
                data=incident.data,
                code=200,
            )
        except Grievance.DoesNotExist:
            return APIResponse(
                success=False,
                message="Grievance not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def get_incidents_list(
        self,
        filters=None,
    ):
        try:
            incidents = self.query_service.get_incidents(
                user=self.user,
                model=Grievance,
                serializer=GetPatientVisitorGrievanceSerializer,
                prefetch_fields=[
                    "documents",
                    "reviews",
                    "review_tasks",
                ],
                related_fields=[
                    "form_initiated_by",
                    "report_facility",
                    "department",
                    "created_by",
                    "complaint_made_by",
                    "administrator_notified",
                    "patient_name",
                    "review_process",
                ],
                filters=filters,
            )
            if not incidents.success:
                return APIResponse(
                    success=False,
                    message=incidents.message,
                    data=None,
                    code=400,
                )

            return APIResponse(
                success=True,
                message="Grievances retrieved successfully",
                data=incidents.data,
                code=200,
            )
        except Exception as e:
            LoggingService().log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def create_incident(self, data) -> APIResponse:
        try:
            facility_response = check_user_facility(data, self.user)
            if not facility_response.success:
                return APIResponse(
                    success=False,
                    message=facility_response.message,
                    data=None,
                    code=400,
                )
            facility = facility_response.data

            request_data = data.copy()
            document_ids = request_data.pop("documents", [])
            review_ids = request_data.pop("reviews", [])
            review_task_ids = request_data.pop("review_tasks", [])

            # Handle user profile relationships
            for field in [
                "form_initiated_by",
                "complaint_made_by",
                "administrator_notified",
                "patient_name",
            ]:
                if field in data:
                    profile = user_profile_service.get_or_create_profile(data[field])
                    if not profile.success:
                        return APIResponse(
                            success=False,
                            message=profile.message,
                            data=None,
                            code=400,
                        )
                    request_data[field] = profile.data.id
            

            request_data["created_by"] = self.user.id
            request_data["report_facility"] = facility.id
            serializer = GrievanceSerializer(data=request_data)

            if serializer.is_valid():
                instance = serializer.save()
                if document_ids:
                    instance.documents.set(Document.objects.filter(id__in=document_ids))
                if review_ids:
                    instance.reviews.set(Review.objects.filter(id__in=review_ids))
                if review_task_ids:
                    instance.review_tasks.set(
                        ReviewProcessTasks.objects.filter(id__in=review_task_ids)
                    )

                # save notification
                save_notification(
                    facility=serializer.data["report_facility"],
                    group_name="Admin",
                    notification_type="info",
                    notification_category="grievance",
                    message="A new grievance is submitted",
                    item_id=serializer.data["id"],
                )

                ActivityService.log_creation(
                    user=self.user,
                    content_object=instance,
                    facility=facility,
                    details={
                        'incident_type': 'Patient/Visitor Grievance',
                        'status': instance.status,
                    }
                )

                if document_ids:
                    ActivityService.log_document_activity(
                        user=self.user,
                        content_object=instance,
                        document_count=len(document_ids),
                        is_addition=True,
                        details={'incident_type': 'Patient/Visitor Grievance'}
                    )

                return APIResponse(
                    success=True,
                    message="Grievance created successfully",
                    data=serializer.data,
                    code=201,
                )
            self.logging_service.log_error(serializer.errors)
            return APIResponse(
                success=False,
                message=serializer.errors,
                data=None,
                code=400,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def update_incident(self, id, data) -> APIResponse:
        try:
            request_data = data.copy()
            grievance = Grievance.objects.get(id=id)
            profile = Profile.objects.get(user=self.user)
            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, profile.facility)
                and not is_manager_user(self.user, grievance.department)
                and not self.user == grievance.created_by
            ):
                return APIResponse(
                    success=False,
                    message="You do not have enough rights to update this grievance",
                    data=None,
                    code=403,
                )
            document_ids = request_data.pop("documents", None)
            review_ids = request_data.pop("reviews", None)
            review_task_ids = request_data.pop("review_tasks", None)
            facility = grievance.report_facility
            request_data["report_facility"] = facility

            # Handle user profile relationships
            for field in [
                "form_initiated_by",
                "complaint_made_by",
                "administrator_notified",
                "patient_name",
            ]:
                if field in data:
                    profile = user_profile_service.get_or_create_profile(data[field])
                    if not profile.success:
                        return APIResponse(
                            success=False,
                            message=profile.message,
                            data=None,
                            code=400,
                        )
                    request_data[field] = profile.data.id

            if "facility_id" in data:
                facility_response = check_user_facility(data, self.user)
                if not facility_response.success:
                    return APIResponse(
                        success=False,
                        message=facility_response.message,
                        code=400,
                    )
                facility = facility_response.data
                request_data["report_facility"] = facility

            if "department" in data:
                try:
                    department = Department.objects.get(id=data["department"])
                    request_data["department"] = department.id
                except Department.DoesNotExist:
                    return APIResponse(
                        success=False,
                        message="Department not found",
                        code=400,
                    )

            data = check_anonymous(request_data, self.user)
            serializer = GrievanceUpdateSerializer(grievance, data=data, partial=True)
            if serializer.is_valid():
                serializer.save()
                if document_ids is not None:
                    grievance.documents.set(
                        Document.objects.filter(id__in=document_ids)
                    )
                if review_ids is not None:
                    grievance.reviews.set(Review.objects.filter(id__in=review_ids))
                if review_task_ids is not None:
                    grievance.review_tasks.set(
                        ReviewProcessTasks.objects.filter(id__in=review_task_ids)
                    )
                if "status" in data and data.get("status") == "Open":
                    send_incident_submission_email(
                        incident=grievance,
                        incident_type="Grievance",
                    )
                serialized_data = GetPatientVisitorGrievanceSerializer(grievance)
                return APIResponse(
                    success=True,
                    message="Grievance updated successfully",
                    data=serialized_data.data,
                    code=200,
                )
            else:
                self.logging_service.log_error(serializer.errors)
                return APIResponse(
                    success=False,
                    message="Invalid data",
                    data=None,
                    code=400,
                )
        except Profile.DoesNotExist:
            return APIResponse(
                success=False,
                message="Profile not found",
                data=None,
                code=404,
            )
        except Grievance.DoesNotExist:
            return APIResponse(
                success=False,
                message="Grievance not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )


class GrievanceInvestigationService:
    def __init__(self, user):
        self.user = user
        self.logging_service = LoggingService()
        self.query_service = IncidentQueryService(
            user=user,
            model=GrievanceInvestigation,
            serializer=GetGrievanceInvestigationSerializer,
        )

    def get_investigation_by_id(self, grievance_id, investigation_id) -> APIResponse:
        try:
            investigation = (
                GrievanceInvestigation.objects.select_related(
                    "report_facility", "grievance_report", "department", "conducted_by"
                )
                .prefetch_related("reviews", "documents", "involved_parties")
                .get(id=investigation_id, grievance_report__id=grievance_id)
            )

            serialized_data = GetGrievanceInvestigationSerializer(investigation).data
            return APIResponse(
                success=True,
                message="Grievance investigation retrieved successfully",
                data=serialized_data,
                code=200,
            )
        except GrievanceInvestigation.DoesNotExist:
            return APIResponse(
                success=False,
                message="Grievance investigation not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False, message="Internal server error", data=None, code=500
            )

    def get_investigations_list(self, grievance_id, filters=None) -> APIResponse:
        try:
            grievance = Grievance.objects.get(id=grievance_id)
            if not grievance:
                return APIResponse(
                    success=False, message="Grievance not found", data=None, code=404
                )
            filters = filters or {}
            filters["grievance_report__id"] = grievance.id
            investigations = self.query_service.get_incidents(
                user=self.user,
                model=GrievanceInvestigation,
                serializer=GetGrievanceInvestigationSerializer,
                prefetch_fields=[
                    "documents",
                    "reviews",
                    "involved_parties",
                ],
                related_fields=[
                    "conducted_by",
                    "grievance_report",
                    "report_facility",
                    "department",
                ],
                filters=filters,
            )
            if not investigations.success:
                return APIResponse(
                    success=False,
                    message=investigations.message,
                    data=None,
                    code=400,
                )
            return APIResponse(
                success=True,
                message="Grievance investigations retrieved successfully",
                data=investigations.data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def create_investigation(self, grievance_id, data) -> APIResponse:
        try:
            grievance = Grievance.objects.get(id=grievance_id)

            if not grievance:
                return APIResponse(
                    success=False, message="Grievance not found", data=None, code=404
                )

            documents = data.pop("documents", [])
            reviews = data.pop("reviews", [])
            extension_letter_copy = data.pop("extension_letter_copy", None)
            response_letter_copy = data.pop("response_letter_copy", None)

            data["grievance_report"] = grievance.id
            data["report_facility"] = grievance.report_facility.id
            data["department"] = (
                grievance.department.id if grievance.department else None
            )

            if "conducted_by" in data:
                profile_result = user_profile_service.get_or_create_profile(
                    data["conducted_by"]
                )
                if not profile_result.success:
                    return APIResponse(
                        success=False, message=profile_result.message, code=400
                    )
                data["conducted_by"] = profile_result.data.id
            
            involved_parties_ids = []
            if "involved_parties" in data:
                for party_data in data["involved_parties"]:
                    party_result = user_profile_service.get_or_create_profile(party_data)
                    if not party_result.success:
                        return APIResponse(
                            success=False, message=party_result.message, code=400
                        )
                    involved_parties_ids.append(party_result.data.id)
                data.pop("involved_parties")

            data["created_by"] = self.user.id
            serializer = GrievanceInvestigationSerializer(data=data)
            if serializer.is_valid():
                investigation = serializer.save()

                if documents:
                    investigation.documents.set(
                        Document.objects.filter(id__in=documents)
                    )
                if reviews:
                    investigation.reviews.set(Review.objects.filter(id__in=reviews))
                if involved_parties_ids:
                    investigation.involved_parties.set(involved_parties_ids)
                if extension_letter_copy:
                    investigation.extension_letter_copy = Document.objects.get(
                        id=extension_letter_copy
                    )
                if response_letter_copy:
                    investigation.response_letter_copy = Document.objects.get(
                        id=response_letter_copy
                    )

                investigation.save()

                return APIResponse(
                    success=True,
                    message="Grievance investigation created successfully",
                    data=GetGrievanceInvestigationSerializer(investigation).data,
                    code=201,
                )
            else:
                return APIResponse(
                    success=False, message=serializer.errors, data=None, code=400
                )
        except Grievance.DoesNotExist:
            return APIResponse(
                success=False, message="Grievance not found", data=None, code=404
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False, message="Internal server error", data=None, code=500
            )

    def update_investigation(self, investigation_id, grievance_id, data) -> APIResponse:
        try:
            grievance = Grievance.objects.get(id=grievance_id)
            if not grievance:
                return APIResponse(
                    success=False, message="Grievance not found", data=None, code=404
                )
            investigation = GrievanceInvestigation.objects.get(id=investigation_id)
            profile = Profile.objects.get(user=self.user)

            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, profile.facility)
                and not is_manager_user(self.user, investigation.department)
                and not self.user == investigation.created_by
            ):
                return APIResponse(
                    success=False,
                    message="You do not have permission to update this investigation",
                    data=None,
                    code=403,
                )

            documents = data.pop("documents", None)
            reviews = data.pop("reviews", None)
            extension_letter_copy = data.pop("extension_letter_copy", None)
            response_letter_copy = data.pop("response_letter_copy", None)

            if "conducted_by" in data:
                profile_result = user_profile_service.get_or_create_profile(
                    data["conducted_by"]
                )
                if not profile_result.success:
                    return APIResponse(
                        success=False, message=profile_result.message, code=400
                    )
                data["conducted_by"] = profile_result.data.id
            
            involved_parties_ids = []
            if "involved_parties" in data:
                for party_data in data["involved_parties"]:
                    party_result = user_profile_service.get_or_create_profile(party_data)
                    if not party_result.success:
                        return APIResponse(
                            success=False, message=party_result.message, code=400
                        )
                    involved_parties_ids.append(party_result.data.id)
                data.pop("involved_parties")
            
            data["updated_by"] = self.user.id

            serializer = GrievanceInvestigationUpdateSerializer(
                investigation, data=data, partial=True
            )
            if serializer.is_valid():
                updated_instance = serializer.save()

                if documents is not None:
                    updated_instance.documents.set(
                        Document.objects.filter(id__in=documents)
                    )
                if reviews is not None:
                    updated_instance.reviews.set(Review.objects.filter(id__in=reviews))
                if involved_parties_ids:
                    updated_instance.involved_parties.set(involved_parties_ids)
                if extension_letter_copy is not None:
                    updated_instance.extension_letter_copy = Document.objects.get(
                        id=extension_letter_copy
                    )
                if response_letter_copy is not None:
                    updated_instance.response_letter_copy = Document.objects.get(
                        id=response_letter_copy
                    )

                updated_instance.save()

                return APIResponse(
                    success=True,
                    message="Grievance investigation updated successfully",
                    data=GetGrievanceInvestigationSerializer(updated_instance).data,
                    code=200,
                )
            else:
                return APIResponse(
                    success=False, message=serializer.errors, data=None, code=400
                )

        except GrievanceInvestigation.DoesNotExist:
            return APIResponse(
                success=False,
                message="Grievance investigation not found",
                data=None,
                code=404,
            )
        except Profile.DoesNotExist:
            return APIResponse(
                success=False, message="Profile not found", data=None, code=404
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False, message="Internal server error", data=None, code=500
            )
