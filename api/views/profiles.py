from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.serializers import ProfileSerializer
from accounts.services.profile.services import ProfileService
from base.services.logging.logger import LoggingService

logging_service = LoggingService()


@api_view(["POST", "GET"])
# @permission_classes([IsAuthenticated])
def profiles_api_view(request):
    profile_service = ProfileService(user=request.user)
    try:
        if request.method == "GET":
            # parameters
            facility = request.query_params.get("facility", None)
            department = request.query_params.get("department", None)
            page = request.query_params.get("page", 1)
            page_size = request.query_params.get("page_size", 10)
            q = request.query_params.get("q", None)
            permissions = request.query_params.get("permissions", None)
            sort_by = request.query_params.get("sort_by", None)
            sort_order = request.query_params.get("sort_order", None)

            params = {
                "facility": facility,
                "department": department,
                "page": page,
                "page_size": page_size,
                "q": q,
                "permissions": permissions,
                "sort_by": sort_by,
                "sort_order": sort_order,
            }

            profiles = profile_service.get_profiles(params)
            if not profiles.success:
                return Response(
                    {"error": profiles.message},
                    status=profiles.code,
                )
            return Response(profiles.data, status=status.HTTP_200_OK)

        elif request.method == "POST":
            profile = profile_service.new_profile(request.data)
            if not profile.success:
                return Response(
                    {"error": profile.message},
                    status=profile.code,
                )
            return Response(
                profile.data,
                status=status.HTTP_201_CREATED,
            )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "PATCH", "DELETE"])
@permission_classes([IsAuthenticated])
def get_user_details_api_view(request, profile_id):
    profile_service = ProfileService(user=request.user)
    try:
        # get user details
        if request.method == "GET":
            user_profile = profile_service.get_user_profile(profile_id)
            if not user_profile.success:
                return Response(
                    {"error": user_profile.message},
                    status=user_profile.code,
                )
            return Response(user_profile.data, status=status.HTTP_200_OK)

        # update user details
        elif request.method == "PUT":
            user_profile = profile_service.update_user_profile(profile_id, request.data)

            if not user_profile.success:
                return Response(
                    {"error": user_profile.message},
                    status=user_profile.code,
                )
            return Response(user_profile.data, status=status.HTTP_200_OK)

        # delete profile
        elif request.method == "DELETE":
            user_profile = profile_service.delete_profile(profile_id)
            if not user_profile.success:
                return Response({"error": user_profile.message}, user_profile.code)
            return Response(status=status.HTTP_204_NO_CONTENT)

        # actions
        if request.method == "PATCH":
            action = request.data.get("action")

            if action == "activate":
                user_profile = profile_service.activate_profile(profile_id)

            elif action == "deactivate":
                user_profile = profile_service.deactivate_profile(profile_id)

            else:
                return Response(
                    {"error": "Invalid action"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not user_profile.success:
                return Response(
                    {"error": user_profile.message},
                    status=user_profile.code,
                )
            return Response(user_profile.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "POST", "DELETE"])
@permission_classes([IsAuthenticated])
def get_user_permissions_api_view(request, profile_id):
    profile_service = ProfileService(user=request.user)
    if request.method == "POST":
        try:
            permissions = profile_service.add_permissions(profile_id, request.data)
            if not permissions.success:
                return Response({"error": permissions.message}, status=permissions.code)
            return Response(permissions.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    elif request.method == "GET":

        try:
            permissions = profile_service.get_user_permissions(request.user, profile_id)

            if not permissions.success:
                return Response({"error": permissions.message}, status=permissions.code)
            return Response(permissions.data, status=status.HTTP_200_OK)

        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    elif request.method == "DELETE":

        try:
            permissions = profile_service.remove_permissions(profile_id, request.data)
            if not permissions.success:
                return Response({"error": permissions.message}, status=permissions.code)
            return Response(permissions.data, status=status.HTTP_200_OK)

        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    else:
        return Response(
            {"error": "Invalid HTTP method"}, status=status.HTTP_405_METHOD_NOT_ALLOWED
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_user_details_api_view(request, user_id):
    profile_service = ProfileService(user=request.user)
    try:

        user_profile = profile_service.update_user_profile(request.user, user_id)
        serializer = ProfileSerializer(user_profile, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_profile_api_view(request, user_id):
    profile_service = ProfileService(user=request.user)
    try:
        user_profile = profile_service.delete_profile(request.user, user_id)
        user_profile.delete()
        return Response(
            {"message": "Profile deleted successfully"}, status=status.HTTP_200_OK
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_user_incidents_api_view(request, profile_id):
    profile_service = ProfileService(user=request.user)
    try:
        filters = request.query_params
        incidents = profile_service.get_user_incidents(profile_id, filters=filters)
        if not incidents.success:
            return Response({"error": incidents.message}, status=incidents.code)
        return Response(incidents.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_user_documents_api_view(request, profile_id):
    profile_service = ProfileService(user=request.user)
    try:
        documents = profile_service.get_user_documents(profile_id)
        if not documents.success:
            return Response({"error": documents.message}, status=documents.code)
        return Response(documents.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def user_complaints_list_api_view(request, profile_id):
    profile_service = ProfileService(user=request.user)
    try:
        complaints = profile_service.get_user_complaints(profile_id)
        if not complaints.success:
            return Response({"error": complaints.message}, status=complaints.code)
        return Response(complaints.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "DELETE"])
@permission_classes([IsAuthenticated])
def user_drafts_api_view(request):
    """
    Handle user draft operations:
    GET: Retrieve all drafts for the authenticated user
    DELETE: Delete multiple drafts for the authenticated user
    """
    profile_service = ProfileService(user=request.user)

    try:
        if request.method == "GET":
            """Get user_id from query params (optional, defaults to current user)"""
            user_id = request.query_params.get("user_id", None)
            if user_id:
                try:
                    user_id = int(user_id)
                except ValueError:
                    return Response(
                        {"error": "Invalid user_id parameter"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            drafts = profile_service.get_user_drafts(user_id)
            if not drafts.success:
                return Response(
                    {"error": drafts.message},
                    status=drafts.code,
                )
            return Response(drafts.data, status=status.HTTP_200_OK)

        elif request.method == "DELETE":
            if not request.data:
                return Response(
                    {"error": "Request body is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            result = profile_service.delete_user_drafts(request.data)
            if not result.success:
                return Response(
                    {"error": result.message, "details": result.data},
                    status=result.code,
                )

            return Response(
                {"message": result.message, "data": result.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
