from django.urls import path

from api.views.profiles import *
from api.views.tasks import tasks_list_create_api, user_tasks_api


urlpatterns = [
    path(
        "",
        profiles_api_view,
        name="profiles_api_view",
    ),  # this is used for Post: create a new profile and Get: get all profiles. It also allows query parameters
    path(
        "<int:profile_id>/",
        get_user_details_api_view,
        name="profile_api_view",
    ),  # this is used for Get: getting a profile details, delete, update and other operations.
    path(
        "<int:profile_id>/permissions/",
        get_user_permissions_api_view,
        name="search_profiles_api_view",
    ),  # this is used to Get: get all permissions for a profile
    path(
        "<int:profile_id>/incidents/",
        get_user_incidents_api_view,
        name="get_user_incidents_api_view",
    ),  # this is used to Get: get all incidents for a profile
    path(
        "<int:profile_id>/tasks/",
        user_tasks_api,
        name="user_tasks_api",
    ),  # this is used to Get: get all tasks for a profile
    path(
        "<int:profile_id>/documents/",
        get_user_documents_api_view,
        name="get_user_documents_api_view",
    ),  # this is used to Get: get all documents for a profile
    path(
        "<int:profile_id>/complaints/",
        user_complaints_list_api_view,
        name="user_complaints_list_api_view",
    ),  # this is used to Get: get all complaints for a profile
]
