from django.urls import path

from api.views.incidents.staff_incident.new_staff_incident import (
    staff_incident_detail_investigation_api,
    staff_incident_investigation_api,
    staff_incident_report_api,
    staff_incident_report_details_api,
    staff_incident_documents_api,
)
from api.views.incidents.staff_incident.versions import staff_incident_version


urlpatterns = [
    path(
        "",
        staff_incident_report_api,
        name="staff_incident_report_api",
    ),
    path(
        "<int:id>/",
        staff_incident_report_details_api,
        name="staff_incident_report_details_api"
    ),
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        staff_incident_version,
        name="staff_incident_version",
    ),
    path(
        "<int:id>/investigation/",
        staff_incident_investigation_api,
        name="staff_incident_report_investigation_api",
    ),
    path(
        "<int:id>/investigation/<int:investigation_id>/",
        staff_incident_detail_investigation_api,
        name="staff_incident_report_detail_investigation_api",
    ),
    path(
        "<int:incident_id>/documents/",
        staff_incident_documents_api,
        name="staff_incident_documents_api",
    ),
]
