from django.urls import path

from api.views.incidents.lost_and_found.new_lost_found import (
    lost_found_incidents_api,
    lost_found_incident_details_api,
    lost_found_incident_documents_api,
)
from api.views.incidents.lost_and_found.version import lost_and_found_incident_version


urlpatterns = [
    path(
        "",
        lost_found_incidents_api,
        name="lost_found_incidents_api",
    ),
    path(
        "<int:id>/",
        lost_found_incident_details_api,
        name="lost_found_incident_details_api",
    ),
    # version
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        lost_and_found_incident_version,
        name="lost_and_found_incident_version",
    ),
    path(
        "<int:incident_id>/documents/",
        lost_found_incident_documents_api,
        name="lost_found_incident_documents_api",
    ),
]