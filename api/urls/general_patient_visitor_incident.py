from django.urls import path

from api.views.incidents.general_incident.general_patient_visitor import (
    general_incidents_api,
    general_incident_details_api,
    general_incident_document_api,
)
from api.views.incidents.general_incident.versions import general_incident_version


urlpatterns = [
    path(
        "",
        general_incidents_api,
        name="general_incidents_api",
    ),
    path(
        "<int:id>/",
        general_incident_details_api,
        name="general_incident_details_api",
    ),
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        general_incident_version,
        name="general_incident_version",
    ),
    path(
        "<int:incident_id>/documents/",
        general_incident_document_api,
        name="general_incident_document_list",
    ),
]
