from django.urls import path
from api.views.activities.list import activities_list, activities_by_content_object
from api.views.activities.new import new_activity, log_activity_for_incident

urlpatterns = [
    path("list/<incident_id>/", activities_list, name="all_activities"),
    path("new/", new_activity, name="new_activity"),

    path("by-content/<int:content_type_id>/<int:object_id>/", activities_by_content_object, name="activities_by_content"),
    path("log/<str:incident_type>/<int:incident_id>/", log_activity_for_incident, name="log_activity_for_incident"),
]
