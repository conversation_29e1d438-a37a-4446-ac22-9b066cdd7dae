from accounts.services.user_profile.service import UserProfileService
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from base.services.incidents.base import IncidentService
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from incidents.services.workflow import IncidentWorkflow
from incidents.views.send_to_department import send_incident_submission_email
from workplace_violence_reports.models import (
    WorkPlaceViolence,
    WorkPlaceViolenceVersion,
)
from workplace_violence_reports.new_serializers import GetWorkplaceViolenceSerializer
from workplace_violence_reports.serializers import WorkPlaceViolenceVersionSerializer
from workplace_violence_reports.services.utils import WorkplaceViolenceUtils
from rest_framework import status
from activities.services import ActivityService
from activities.models import ActivityType
from base.constants import ReviewStatus

user_profile_service = UserProfileService()
logging_service = LoggingService()
utilities = WorkplaceViolenceUtils()


class WorkplaceViolenceActions:
    """
    This class contains methods to handle actions related to workplace violence reports.
    """

    def __init__(self, user, incident_id, data):
        self.user = user
        self.data = data
        self.incident_id = incident_id
        self.workflow_services = IncidentWorkflow(
            model=WorkPlaceViolence, user=self.user
        )
        self.general_incident = IncidentService()

        try:
            self.incident = (
                WorkPlaceViolence.objects.select_related(
                    "review_process",
                    "name_of_supervisor",
                    "reported_by",
                    "report_facility",
                    "department",
                    "created_by",
                )
                .prefetch_related(
                    "documents",
                    "reviews",
                    "review_tasks",
                    "persons_injured",
                    "incident_witness",
                    "involved_parties",
                )
                .get(id=incident_id)
            )
        except WorkPlaceViolence.DoesNotExist:
            self.incident = None


    def modify_incident(self, report_data) -> APIResponse:
        """"""
        # Logic to create a
        try:
            # Fetch the incident to be updated
            incident = WorkPlaceViolence.objects.get(id=self.incident_id)
            new_version = WorkPlaceViolenceVersion.objects.create(
                original_report=incident
            )
            report_facility = incident.report_facility

            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, incident.report_facility)
                and not is_manager_user(self.user, incident.department)
            ) and not incident.created_by == self.user:
                return APIResponse(
                    success=False,
                    code=status.HTTP_403_FORBIDDEN,
                    message="You do not have permission to modify this incident.",
                )
            # handle related data
            # print(f"data: {self.data}")
            related_result = self.handle_related_data(new_version, self.data)
            if isinstance(related_result, APIResponse) and not related_result.success:
                return related_result   
            # create a new version of the incident
            self.data["original_report"] = incident.id
            self.data["created_by"] = self.user.id
            serializer = WorkPlaceViolenceVersionSerializer(data=self.data)
            if not serializer.is_valid():
                logging_service.get_serializer_error_message(serializer)
                return APIResponse(
                    success=False,
                    code=status.HTTP_400_BAD_REQUEST,
                    message=serializer.errors,
                )
            updated_instance = serializer.update(new_version, serializer.validated_data)
            updated_instance.refresh_from_db()
            old_status = incident.status
            incident.is_modified = True
            incident.status = self.data.get("status", "Draft")
            incident.save()

            ActivityService.log_activity(
                user=self.user,
                content_object=incident,
                activity_type=ActivityType.UPDATED,
                description="Incident modified",
                details={
                    'incident_type': 'Workplace Violence Report',
                    'modification_type': 'incident_update'
                }
            )

            new_status = incident.status
            if old_status != new_status:
                ActivityService.log_status_change(
                    user=self.user,
                    content_object=incident,
                    old_status=old_status,
                    new_status=new_status,
                    details={
                        'incident_type': 'Workplace Violence Report',
                        'status_change': 'modification_status_change'
                    }
                )

            response_data = WorkPlaceViolenceVersionSerializer(updated_instance).data
            if "status" in self.data and self.data.get("status") == "Open":
                send_incident_submission_email(
                    incident=incident,
                    incident_type="Workplace Violence Report",
                )
            return APIResponse(
                success=True,
                code=status.HTTP_200_OK,
                message="Incident modified successfully.",
                data=response_data,
            )

        except WorkPlaceViolence.DoesNotExist:
            return APIResponse(
                success=False,
                code=status.HTTP_404_NOT_FOUND,
                message="Incident not found.",
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                message="An error occurred while modifying the incident.",
            )

    def send_for_review(self) -> APIResponse:
        response = self.workflow_services.send_for_a_review(
            self.incident_id,
            self.data,
        )

        if not response.success:
            return APIResponse(
                success=False,
                message=response.message,
                code=response.code or status.HTTP_400_BAD_REQUEST,
                data=None,
            )

        assignees = response.data.assignees.all()
        if assignees.exists():
            for assignee in assignees:
                ActivityService.log_review_activity(
                    user=self.user,
                    content_object=response.data,
                    review_type="workflow_review",
                    description="Incident sent for review",
                    details={
                        'incident_type': 'Workplace Violence Report',
                        'review_assignment': 'individual_assignee'
                    },
                    target_user=assignee
                )
        else:
            ActivityService.log_review_activity(
                user=self.user,
                content_object=response.data,
                review_type="workflow_review",
                description="Incident sent for review",
                details={
                    'incident_type': 'Workplace Violence Report',
                    'review_assignment': 'general_review'
                }
            )

        serializer = GetWorkplaceViolenceSerializer(response.data)
        return APIResponse(
            success=True,
            message="Incident sent for review successfully",
            code=status.HTTP_200_OK,
            data=serializer.data,
        )

    def mark_closed(self) -> APIResponse:
        response = self.workflow_services.mark_as_resolved(
            incident=self.incident,
            user=self.user,
            # is_dynamic=True,
        )
        if not response.success:
            return APIResponse(
                success=False,
                message=response.message,
                code=status.HTTP_400_BAD_REQUEST,
                data=None,
            )

        ActivityService.log_status_change(
            user=self.user,
            content_object=self.incident,
            old_status=self.incident.status,
            new_status=ReviewStatus.CLOSED,
            details={
                'incident_type': 'Workplace Violence',
                'status_change': 'closed'
            }
        )

        return APIResponse(
            success=True,
            message="Incident marked as closed successfully",
            code=status.HTTP_200_OK,
            data=None,
        )

    def delete_draft_incident(self) -> APIResponse:
        """
        This method deletes draft incidents.
        """

        try:

            response = self.workflow_services.delete_drafts(
                self.user,
                WorkPlaceViolence,
                self.data.get("incident_ids"),
            )
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=status.HTTP_400_BAD_REQUEST,
                    data=None,
                )

            ActivityService.log_activity(
                user=self.user,
                content_object=self.incident,
                activity_type=ActivityType.DELETED,
                description="Draft incident deleted",
                details={
                    'incident_type': 'Workplace Violence',
                    'deletion_type': 'draft_deletion'
                }
            )

            return APIResponse(
                success=True,
                message="Draft incidents deleted successfully",
                code=status.HTTP_200_OK,
                data=response.data,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while deleting draft incidents",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )

    def handle_related_data(self, new_version, data) -> APIResponse:

        if "involved_parties" in data:

            initiated_by_profile = utilities.handle_involved_parties(
                data.pop("involved_parties", None), new_version
            )
            if not initiated_by_profile.success:
                return APIResponse(
                    code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                    message=initiated_by_profile.message,
                )

        if "incident_witnesses" in data:
            incident_witness_profile = utilities.handle_incident_witnesses(
                data.pop("incident_witnesses"), new_version
            )
            if not incident_witness_profile.success:
                return APIResponse(
                    success=False,
                    code=status.HTTP_400_BAD_REQUEST,
                    message=incident_witness_profile.message,
                )

        if "persons_injured" in self.data:
            person_injured = utilities.handle_person_injured(data, new_version)
            if not person_injured.success:
                return APIResponse(
                    success=False,
                    code=status.HTTP_400_BAD_REQUEST,
                    message=person_injured.message,
                )
            data.pop("persons_injured", None)
        if "name_of_supervisor" in data:
            name_of_supervisor_profile = user_profile_service.get_or_create_profile(
                data["name_of_supervisor"]
            )
            if not name_of_supervisor_profile.success:
                return APIResponse(
                    success=False,
                    code=status.HTTP_400_BAD_REQUEST,
                    message=name_of_supervisor_profile.message,
                )
            data["name_of_supervisor"] = name_of_supervisor_profile.data.id

        if "reported_by" in data:

            reported_by_profile = user_profile_service.get_or_create_profile(
                data["reported_by"]
            )
            if not reported_by_profile.success:
                return APIResponse(
                    success=False,
                    code=status.HTTP_400_BAD_REQUEST,
                    message=reported_by_profile.message,
                )
            data["reported_by"] = reported_by_profile.data.id
