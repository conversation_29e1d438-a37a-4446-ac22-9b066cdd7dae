from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from incidents.services.query import IncidentQueryService
from workplace_violence_reports.models import WorkPlaceViolence
from workplace_violence_reports.new_serializers import GetWorkplaceViolenceSerializer
from workplace_violence_reports.serializers import WorkPlaceViolenceSerializer
from workplace_violence_reports.services.actions import WorkplaceViolenceActions
from activities.services import ActivityService
from activities.models import ActivityType


class WorkplaceOperations:
    def __init__(self, user):
        self.logging_service = LoggingService()
        self.user = user
        self.query_service = IncidentQueryService(
            user=user,
            model=WorkPlaceViolence,
            serializer=GetWorkplaceViolenceSerializer,
        )

    def get_incidents_list(self, filters=None) -> APIResponse:
        """
        Get a list of incidents based on the user's permissions and the specified facility or department.
        """
        try:
            incidents = self.query_service.get_incidents(
                user=self.user,
                model=WorkPlaceViolence,
                serializer=GetWorkplaceViolenceSerializer,
                filters=filters,
                prefetch_fields=[
                    "documents",
                    "reviews",
                    "review_tasks",
                    "persons_injured",
                    "incident_witness",
                    "involved_parties",
                ],
                related_fields=[
                    "review_process",
                    "name_of_supervisor",
                    "reported_by",
                    "report_facility",
                    "department",
                    "created_by",
                ],
            )
            if not incidents.success:
                return APIResponse(
                    success=False,
                    message=incidents.message,
                    data=None,
                    code=400,
                )
            return APIResponse(
                success=True,
                message="Incidents retrieved successfully",
                data=incidents.data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def get_incident_by_id(self, incident_id) -> APIResponse:
        """
        Get a specific incident by its ID.
        """
        try:
            incident = self.query_service.get_incident_by_id(
                incident_id=incident_id,
            )
            if not incident.success:
                return APIResponse(
                    success=False,
                    message=incident.message,
                    data=None,
                    code=400,
                )
            return APIResponse(
                success=True,
                message="Incident retrieved successfully",
                data=incident.data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def create_incident(self, data, user) -> APIResponse:
        try:
            # create a new incident
            """The following lines are commented because on first step of the form, we don't have all the data"""
            new_incident = WorkPlaceViolence.objects.create(
                # type_of_incident=data.get("type_of_incident", None),
                # incident_type=data.get("incident_type", None),
                physical_injury_description=data.get(
                    "physical_injury_description", None
                ),
                date_of_incident=data.get("date_of_incident", None),
                time_of_incident=data.get("time_of_incident", None),
                created_by=user,
            )

            # update created incident with additional data

            # handle related data
            WorkplaceViolenceActions(
                self.user, incident_id=new_incident.id, data=data
            ).handle_related_data(
                new_incident,
                data,
            )

            data["created_by"] = user.id
            serializer = WorkPlaceViolenceSerializer(instance=new_incident, data=data)
            if not serializer.is_valid():
                self.logging_service.get_serializer_error_message(serializer)
                return APIResponse(
                    success=False,
                    message=serializer.errors,
                    code=400,
                )
            serializer.update(new_incident, serializer.validated_data)

            ActivityService.log_creation(
                user=user,
                content_object=new_incident,
                facility=getattr(new_incident, 'report_facility', None),
                details={
                    'incident_type': 'Workplace Violence',
                    'status': new_incident.status,
                }
            )

            document_ids = data.get('document_ids', [])
            if document_ids:
                ActivityService.log_document_activity(
                    user=user,
                    content_object=new_incident,
                    document_count=len(document_ids),
                    is_addition=True,
                    details={'incident_type': 'Workplace Violence'}
                )

            return APIResponse(
                success=True,
                message="Incident created successfully.",
                data=serializer.data,
                code=201,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while creating the incident.",
                code=500,
            )

    def update_incident(self, incident_id, data) -> APIResponse:
        try:
            # get the incident
            incident = WorkPlaceViolence.objects.get(id=incident_id)

            # handle related data
            WorkplaceViolenceActions(
                self.user, incident_id=incident.id, data=data
            ).handle_related_data(
                incident,
                data,
            )
            # update the incident with new data
            data["updated_by"] = self.user.id
            serializer = WorkPlaceViolenceSerializer(instance=incident, data=data)
            if not serializer.is_valid():
                self.logging_service.get_serializer_error_message(serializer)
                return APIResponse(
                    success=False,
                    message=serializer.errors,
                    code=400,
                )
            serializer.update(incident, serializer.validated_data)
            return APIResponse(
                success=True,
                message="Incident updated successfully.",
                data=serializer.data,
                code=200,
            )
        except WorkPlaceViolence.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found.",
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while updating the incident.",
                code=500,
            )
