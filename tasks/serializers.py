from rest_framework import serializers, viewsets
from django.db.models import Count
from accounts.serializers import UserSerializer
from tasks.models import (
    ReviewGroups,
    ReviewProcessTasks,
    ReviewTemplateTasks,
    ReviewTemplates,
)


class ReviewGroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewGroups
        fields = [
            "id",
            "title",
            "description",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class GetReviewGroupSerializer(serializers.ModelSerializer):
    members = serializers.IntegerField(source="members_count", read_only=True)

    class Meta:
        model = ReviewGroups
        fields = [
            "id",
            "title",
            "description",
            "members",
            "created_at",
            "updated_at",
        ]


class ReviewGroupViewSet(viewsets.ModelViewSet):
    queryset = ReviewGroups.objects.all()
    serializer_class = GetReviewGroupSerializer

    def get_queryset(self):
        return ReviewGroups.objects.annotate(members_count=Count("members"))


class ReviewTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewTemplates
        fields = "__all__"


class ReviewTemplateTaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewTemplateTasks
        fields = "__all__"


class GetReviewTemplateTaskSerializer(serializers.ModelSerializer):
    review_template = serializers.SerializerMethodField()
    review_groups = serializers.SerializerMethodField()

    class Meta:
        model = ReviewTemplateTasks
        fields = "__all__"

    def get_review_template(self, obj):
        if obj.review_template:
            return {"id": obj.review_template.id, "name": obj.review_template.name}
        return None

    def get_review_groups(self, obj):
        return [
            {"id": group.id, "name": group.title} for group in obj.review_groups.all()
        ]


class GetReviewTemplateSerializer(serializers.ModelSerializer):
    tasks = ReviewTemplateTaskSerializer(many=True, read_only=True)
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)

    class Meta:
        model = ReviewTemplates
        fields = "__all__"


class ReviewProcessTasksSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewProcessTasks
        fields = "__all__"


class GetReviewProcessTasksSerializer(serializers.ModelSerializer):
    review_process = serializers.SerializerMethodField()
    groups_completed = serializers.SerializerMethodField()
    reviewers = serializers.SerializerMethodField()
    review_groups = serializers.SerializerMethodField()
    reviewers_completed = serializers.SerializerMethodField()

    class Meta:
        model = ReviewProcessTasks
        fields = "__all__"

    def get_review_process(self, obj):
        if obj.review_process:
            return {"id": obj.review_process.id, "name": obj.review_process.name}
        return None

    def get_groups_completed(self, obj):
        # a list of group, which each group having id and name
        if obj.review_process:
            return {"id": obj.review_process.id, "name": obj.review_process.name}
        return None

    def get_groups_completed(self, obj):
        if obj.groups_completed:
            return [
                {"id": group.id, "name": group.title}
                for group in obj.groups_completed.all()
            ]
        return []

    def get_reviewers(self, obj):
        if obj.reviewers:
            return [
                {
                    "id": reviewer.id,
                    "name": f"{reviewer.user.first_name} {reviewer.user.last_name}".strip(),
                }
                for reviewer in obj.reviewers.all()
            ]
        return []

    def get_reviewers_completed(self, obj):
        if obj.reviewers_completed:
            return [
                {
                    "id": reviewer.id,
                    "name": f"{reviewer.user.first_name} {reviewer.user.last_name}".strip(),
                }
                for reviewer in obj.reviewers_completed.all()
            ]
        return []

    def get_review_groups(self, obj):
        if obj.review_groups:
            return [
                {"id": group.id, "name": group.title}
                for group in obj.review_groups.all()
            ]
        return []
