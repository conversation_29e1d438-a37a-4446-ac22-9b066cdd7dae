from django.db import models
from accounts.models import Profile
from base.models import BaseModel

# Create your models here.


class ReviewGroups(BaseModel):
    title = models.CharField(max_length=255, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    members = models.ManyToManyField(
        Profile,
        related_name="review_group_members",
        blank=True,
    )


class ReviewTemplates(BaseModel):
    incident_type = models.CharField(max_length=255, null=True, blank=True)
    name = models.CharField(max_length=255, null=True, blank=True)
    description = models.TextField(null=True, blank=True)


class ReviewProcess(BaseModel):
    name = models.CharField(max_length=255, null=True, blank=True)
    description = models.TextField(null=True, blank=True)


class ReviewTemplateTasks(BaseModel):
    name = models.CharField(max_length=255, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    review_template = models.ForeignKey(
        ReviewTemplates,
        related_name="review_template_tasks",
        on_delete=models.CASCADE,
        null=True,
    )
    review_groups = models.ManyToManyField(
        ReviewGroups,
        related_name="review_template_groups",
        blank=True,
    )
    require_approval_for_all_groups = models.BooleanField(default=False)
    task_priority = models.IntegerField(default=1)
    number_of_days_to_complete = models.IntegerField(null=True, blank=True, default=3)
    depends_on = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="dependent_tasks",
        help_text="Task that must be completed before this task can start.",
    )

    # check for task priority, and if it exists, increment it by 1
    # def save(self, *args, **kwargs):
    #     if self.pk is not None:
    #         existing_task = ReviewTemplateTasks.objects.get(pk=self.pk)
    #         if existing_task.task_priority != self.task_priority:
    #             self.task_priority = existing_task.task_priority + 1
    #     super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name}"


class ReviewProcessTasks(ReviewTemplateTasks):
    TASK_STATUS_CHOICES = [
        ("Pending", "Pending"),
        ("In Progress", "In Progress"),
        ("Submitted", "Submitted"),
        ("Completed", "Completed"),
    ]

    review_process = models.ForeignKey(
        ReviewProcess,
        related_name="review_process_tasks",
        on_delete=models.CASCADE,
    )
    status = models.CharField(
        max_length=255,
        choices=TASK_STATUS_CHOICES,
        default="Pending",
    )
    groups_completed = models.ManyToManyField(
        ReviewGroups,
        related_name="review_group_completed",
        blank=True,
    )
    reviewers = models.ManyToManyField(
        Profile,
        related_name="task_reviewers",
        blank=True,
    )
    reviewers_completed = models.ManyToManyField(
        Profile,
        related_name="task_reviewers_completed",
        blank=True,
    )
    require_all_members_to_complete = models.BooleanField(default=False)
    deadline = models.DateField(null=True, blank=True)

    class Meta:
        permissions = [
            ("can_approve_task", "Can approve task"),
            ("can_complete_task", "Can complete task"),
            ("can_mark_task_completed", "Can mark task as completed"),
            ("can_create_task", "Can create task"),
        ]
