# Task Actions API Documentation

This document describes how to use the Task Actions API endpoints for submitting, completing, and approving tasks in the quality control system.

## Base Endpoint

All task actions use the same endpoint with different action parameters:

```
PATCH /api/tasks/{task_id}/
```

## Authentication

All endpoints require authentication. Include the authorization token in your request headers:

```
Authorization: Bearer <your_jwt_token>
```

## Request Format

All actions use the same request structure:

```json
{
	"action": "submit|complete|approve"
}
```

---

## 1. Submit Task Action

### Purpose

Submit a task for review. This action can be performed by:

- **Group members** assigned to the task's review groups
- **Individual reviewers** assigned to the task

### Request Example

```http
PATCH /api/tasks/123/
Content-Type: application/json
Authorization: Bearer <token>

{
    "action": "submit"
}
```

### Success Response

```json
HTTP 200 OK
{
    "id": 123,
    "name": "Task Name",
    "status": "Submitted", // or "In Progress" depending on task configuration
    "description": "Task description",
    // ... other task fields
}
```

### Behavior

#### For Group Members:

- If `require_approval_for_all_groups = false`: Task status → **"Submitted"**
- If `require_approval_for_all_groups = true`: Task status → **"In Progress"**
- The user's review group is added to `groups_completed`

#### For Individual Reviewers:

- Task status → **"In Progress"**
- The reviewer is added to `reviewers_completed`

### Error Cases

| Status Code | Scenario                                | Message                                                  |
| ----------- | --------------------------------------- | -------------------------------------------------------- |
| 400         | User not in review group/not a reviewer | "You do not have permission to submit this task"         |
| 400         | User already submitted as reviewer      | "You have already submitted this task as a reviewer"     |
| 400         | User's group already submitted          | "You have already submitted this task as a group member" |
| 400         | Task not found                          | "Task not found"                                         |

---

## 2. Complete Task Action

### Purpose

Mark a task as completed. This action can be performed by:

- **Quality/Risk Managers** (always allowed)
- **Authorized reviewers** (depending on task configuration)
- **Authorized group members** (depending on task configuration)

### Request Example

```http
PATCH /api/tasks/123/
Content-Type: application/json
Authorization: Bearer <token>

{
    "action": "complete"
}
```

### Success Response

```json
HTTP 200 OK
{
    "id": 123,
    "name": "Task Name",
    "status": "Completed",
    "description": "Task description",
    // ... other task fields
}
```

### Permissions

| User Type            | Permission        | Notes                                   |
| -------------------- | ----------------- | --------------------------------------- |
| Quality/Risk Manager | ✅ Always allowed | Bypass all restrictions                 |
| Individual Reviewer  | ✅ Conditional    | Must be assigned to task                |
| Group Member         | ✅ Conditional    | Must be member of assigned review group |
| Other Users          | ❌ Denied         | No permission                           |

### Task Configuration Impact

- `require_all_members_to_complete`: Affects whether all reviewers must complete before final completion
- `require_approval_for_all_groups`: Affects whether all groups must complete before final completion

### Error Cases

| Status Code | Scenario                   | Message                                            |
| ----------- | -------------------------- | -------------------------------------------------- |
| 400         | Unauthorized user          | "You do not have permission to complete this task" |
| 400         | Already completed by user  | "You have already completed this task"             |
| 400         | Already completed by group | "Your group has already completed this task"       |
| 400         | Task not found             | "Task not found"                                   |

---

## 3. Approve Task Action

### Purpose

Approve a completed task. This action is **restricted** to:

- **Quality/Risk Managers only**

### Request Example

```http
PATCH /api/tasks/123/
Content-Type: application/json
Authorization: Bearer <token>

{
    "action": "approve"
}
```

### Success Response

```json
HTTP 200 OK
{
    "id": 123,
    "name": "Task Name",
    "status": "Approved",
    "description": "Task description",
    // ... other task fields
}
```

### Permissions

| User Type            | Permission | Notes                                   |
| -------------------- | ---------- | --------------------------------------- |
| Quality/Risk Manager | ✅ Allowed | Must be in "Quality/Risk Manager" group |
| All Other Users      | ❌ Denied  | Including reviewers and group members   |

### Error Cases

| Status Code | Scenario                      | Message                                           |
| ----------- | ----------------------------- | ------------------------------------------------- |
| 400         | Non-Quality/Risk Manager user | "You do not have permission to approve this task" |
| 400         | Task not found                | "Task not found"                                  |

---

## Usage Examples

### JavaScript/Fetch Example

```javascript
// Submit a task
async function submitTask(taskId, token) {
	const response = await fetch(`/api/tasks/${taskId}/`, {
		method: "PATCH",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${token}`,
		},
		body: JSON.stringify({
			action: "submit",
		}),
	});

	if (response.ok) {
		const data = await response.json();
		console.log("Task submitted:", data);
		return data;
	} else {
		const error = await response.json();
		console.error("Submit failed:", error.message);
		throw new Error(error.message);
	}
}

// Complete a task
async function completeTask(taskId, token) {
	const response = await fetch(`/api/tasks/${taskId}/`, {
		method: "PATCH",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${token}`,
		},
		body: JSON.stringify({
			action: "complete",
		}),
	});

	return response.json();
}

// Approve a task (Quality/Risk Manager only)
async function approveTask(taskId, token) {
	const response = await fetch(`/api/tasks/${taskId}/`, {
		method: "PATCH",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${token}`,
		},
		body: JSON.stringify({
			action: "approve",
		}),
	});

	return response.json();
}
```

### Python/Requests Example

```python
import requests

def perform_task_action(task_id, action, token):
    """
    Perform a task action (submit, complete, approve)

    Args:
        task_id (int): The ID of the task
        action (str): The action to perform ('submit', 'complete', 'approve')
        token (str): JWT authentication token

    Returns:
        dict: Response data or raises exception
    """
    url = f"/api/tasks/{task_id}/"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    data = {'action': action}

    response = requests.patch(url, json=data, headers=headers)

    if response.status_code == 200:
        return response.json()
    else:
        error_data = response.json()
        raise Exception(f"Action failed: {error_data.get('message', 'Unknown error')}")

# Usage examples
token = "your_jwt_token_here"

# Submit task
try:
    result = perform_task_action(123, 'submit', token)
    print(f"Task submitted successfully: {result['status']}")
except Exception as e:
    print(f"Submit failed: {e}")

# Complete task
try:
    result = perform_task_action(123, 'complete', token)
    print(f"Task completed successfully: {result['status']}")
except Exception as e:
    print(f"Complete failed: {e}")

# Approve task (requires Quality/Risk Manager role)
try:
    result = perform_task_action(123, 'approve', token)
    print(f"Task approved successfully: {result['status']}")
except Exception as e:
    print(f"Approve failed: {e}")
```

---

## Task Status Flow

```
Pending → [submit] → Submitted/In Progress → [complete] → Completed → [approve] → Approved
```

### Status Descriptions

- **Pending**: Initial state, task awaits action
- **Submitted**: Group task submitted (when `require_approval_for_all_groups = false`)
- **In Progress**: Task is being worked on by reviewers
- **Completed**: Task work is finished, awaiting approval
- **Approved**: Task is officially approved (final state)

---

## Important Notes

1. **Group vs Individual Tasks**: The behavior differs based on whether the task is assigned to review groups or individual reviewers.

2. **Configuration Flags**: Task behavior is influenced by:

      - `require_approval_for_all_groups`
      - `require_all_members_to_complete`

3. **Permission Hierarchy**: Quality/Risk Managers have the highest permissions and can bypass most restrictions.

4. **Idempotency**: Attempting the same action twice will result in an error to prevent duplicate submissions.

5. **Authentication Required**: All endpoints require valid JWT authentication.

6. **Error Handling**: Always check the response status code and handle error messages appropriately in your client application.
