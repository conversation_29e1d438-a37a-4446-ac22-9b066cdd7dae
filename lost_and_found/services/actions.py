from accounts.services.user_profile.service import UserProfileService
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from base.constants import ReviewStatus
from base.services.incidents.base import IncidentService
from base.services.responses import APIResponse
from lost_and_found.models import LostAndFound
from base.services.logging.logger import LoggingService
from lost_and_found.serializers import LostAndFoundVersionSerializer
from lost_and_found.new_serializers import GetLostAndFoundSerializer
from incidents.services.workflow import IncidentWorkflow
from rest_framework import status
from django.core.exceptions import ValidationError
from incidents.views.send_to_department import send_incident_submission_email
from activities.services import ActivityService
from activities.models import ActivityType

logging_service = LoggingService()
user_profile_service = UserProfileService()

class LostFoundActionsService:
    """
    Service class for handling Lost and Found incident actions.
    """

    def __init__(self, user, incident_id, data):
        self.user = user
        self.data = data
        self.incident_id = incident_id
        self.workflow_services = IncidentWorkflow(
            model=LostAndFound, user=self.user
        )
        self.general_incident = IncidentService()
        try:
            self.incident = (
                LostAndFound.objects.select_related(
                    "report_facility",
                    "department",
                    "created_by",
                    "reported_by",
                    "taken_by",
                    "found_by"
                )
                .prefetch_related(
                    "documents",
                    "reviews",
                )
                .get(id=incident_id)
            )
        except LostAndFound.DoesNotExist:
            self.incident = None

    def modify_incident(self) -> APIResponse:
        """Modifies an existing Lost and Found incident."""
        try:
            report_facility = self.incident.report_facility
            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, self.incident.report_facility)
                and not is_manager_user(self.user, self.incident.department)
            ) and not self.incident.created_by == self.user:
                return APIResponse(
                    success=False,
                    message="You do not have permission to modify this incident",
                    code=status.HTTP_403_FORBIDDEN,
                    data=None
                )
            
            if "found_by" in self.data:
                found_by = user_profile_service.get_or_create_profile(
                    self.data["found_by"]
                )
                if not found_by.success:
                    return APIResponse(
                        success=False,
                        message=found_by.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None
                    )
                self.data["found_by"] = found_by.data.id
            if "reported_by" in self.data:
                reported_by = user_profile_service.get_or_create_profile(
                    self.data["reported_by"]
                )
                if not reported_by.success:
                    return APIResponse(
                        success=False,
                        message=reported_by.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None
                    )
                self.data["reported_by"] = reported_by.data.id
            
            if "taken_by" in self.data:
                taken_by = user_profile_service.get_or_create_profile(
                    self.data["taken_by"]
                )
                if not taken_by.success:
                    return APIResponse(
                        success=False,
                        message=taken_by.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None
                    )
                self.data["taken_by"] = taken_by.data.id
            
            self.data["report_facility"] = report_facility.id
            self.data["department"] = self.incident.department.id
            self.data["created_by"] = self.incident.created_by.id
            self.data["original_report"] = self.incident.id

            version_serializer = LostAndFoundVersionSerializer(
                data=self.data
            )
            if version_serializer.is_valid():
                version_serializer.save()
                old_status = self.incident.status
                self.incident.is_modified = True
                self.incident.updated_by = self.user
                self.incident.status = self.data.get("status", ReviewStatus.DRAFT)
                self.incident.save()

                ActivityService.log_activity(
                    user=self.user,
                    content_object=self.incident,
                    activity_type=ActivityType.UPDATED,
                    description="Incident modified",
                    details={
                        'incident_type': 'Lost and Found',
                        'modification_type': 'incident_update'
                    }
                )

                new_status = self.incident.status
                if old_status != new_status:
                    ActivityService.log_status_change(
                        user=self.user,
                        content_object=self.incident,
                        old_status=old_status,
                        new_status=new_status,
                        details={
                            'incident_type': 'Lost and Found',
                            'status_change': 'modification_status_change'
                        }
                    )

                if self.incident.status == ReviewStatus.OPEN:
                    send_incident_submission_email(
                        incident=self.incident,
                        incident_type="Lost and Found",
                        )
                return APIResponse(
                    success=True,
                    message="Lost and Found incident modified successfully",
                    code=status.HTTP_200_OK,
                    data=version_serializer.data
                )
            else:
                logging_service.log_error(version_serializer.errors)
                return APIResponse(
                    success=False,
                    message="Invalid data provided",
                    code=status.HTTP_400_BAD_REQUEST,
                    data=version_serializer.errors
                )
        except LostAndFound.DoesNotExist:
            return APIResponse(
                success=False,
                message="Lost and Found incident not found",
                code=status.HTTP_404_NOT_FOUND,
                data=None
            )
        except ValidationError as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Validation error",
                code=status.HTTP_400_BAD_REQUEST,
                data=None
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error modifying incident",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None
            )
            


    def send_for_review(self) -> APIResponse:
        """Sends the incident for review."""
        try:
            response = self.workflow_services.send_for_a_review(
                self.incident_id,
                self.data
            )
            
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None
                )

            assignees = response.data.assignees.all()
            if assignees.exists():
                for assignee in assignees:
                    ActivityService.log_review_activity(
                        user=self.user,
                        content_object=response.data,
                        review_type="workflow_review",
                        description="Incident sent for review",
                        details={
                            'incident_type': 'Lost and Found',
                            'review_assignment': 'individual_assignee'
                        },
                        target_user=assignee
                    )
            else:
                ActivityService.log_review_activity(
                    user=self.user,
                    content_object=response.data,
                    review_type="workflow_review",
                    description="Incident sent for review",
                    details={
                        'incident_type': 'Lost and Found',
                        'review_assignment': 'general_review'
                    }
                )

            serializer = GetLostAndFoundSerializer(response.data)
            return APIResponse(
                success=True,
                message="Lost and Found incident sent for review",
                code=status.HTTP_200_OK,
                data=serializer.data
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error sending incident for review",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None
            )

    def mark_closed(self) -> APIResponse:
        """Marks the incident as resolved."""
        try:
            response = self.workflow_services.mark_as_resolved(
                incident=self.incident,
                user=self.user
            )

            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None
                )

            ActivityService.log_status_change(
                user=self.user,
                content_object=self.incident,
                old_status=self.incident.status,
                new_status=ReviewStatus.CLOSED,
                details={
                    'incident_type': 'Lost and Found',
                    'status_change': 'closed'
                }
            )

            return APIResponse(
                success=True,
                message="Lost and Found incident marked as resolved",
                code=status.HTTP_200_OK,
                data=None
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error marking incident as resolved",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None
            )
    
    def delete_lost_found_draft_incidents(self) -> APIResponse:
        """Deletes draft incidents for Lost and found."""
        try:
            response = self.workflow_services.delete_drafts(
                user=self.user,
                model=LostAndFound,
                incident_id=self.data.get("incident_ids", None),
            )
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None
                )

            ActivityService.log_activity(
                user=self.user,
                content_object=self.incident,
                activity_type=ActivityType.DELETED,
                description="Draft incident deleted",
                details={
                    'incident_type': 'Lost and Found',
                    'deletion_type': 'draft_deletion'
                }
            )

            return APIResponse(
                success=True,
                message="Draft incidents deleted successfully",
                code=status.HTTP_200_OK,
                data=response.data
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error deleting draft incidents",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None
            )